// components/ProductSearch.tsx
import React, { useState, useCallback, useEffect } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box } from "@mui/material";
import { useNavigate } from "react-router-dom";
import FilterRow from "../../components/FilterRow";
import { debounce } from "@mui/material/utils";

// 📝 LEARNING NOTE: Product interface matching your API response structure
interface Product {
  PRDNO: string;
  DESCP: string;
  ItemID: string;
  SkuName: string;
  AssociatedItem: string;
  REUPC?: string;
  Brand?: string;
  SalesDesc?: string;
}

// 📝 LEARNING NOTE: Mock data for products (since no backend API yet)
const mockProducts: Product[] = [
  {
    PRDNO: "PRD001",
    DESCP: "Premium Dog Food - Chicken & Rice",
    ItemID: "ITM001",
    SkuName: "PEDIGREE-ADULT-CHICKEN-15KG",
    AssociatedItem: "DOG-TREATS-001",
    REUPC: "123456789012",
    Brand: "Pedigree",
    SalesDesc: "Complete nutrition for adult dogs with real chicken"
  },
  {
    PRDNO: "PRD002", 
    DESCP: "Cat Food - Salmon & Tuna",
    ItemID: "ITM002",
    SkuName: "WHISKAS-ADULT-SALMON-5KG",
    AssociatedItem: "CAT-TREATS-001",
    REUPC: "123456789013",
    Brand: "Whiskas",
    SalesDesc: "Delicious salmon and tuna flavor for adult cats"
  },
  {
    PRDNO: "PRD003",
    DESCP: "Puppy Training Treats",
    ItemID: "ITM003", 
    SkuName: "PEDIGREE-PUPPY-TREATS-500G",
    AssociatedItem: "TRAINING-COLLAR-001",
    REUPC: "123456789014",
    Brand: "Pedigree",
    SalesDesc: "Perfect training rewards for puppies"
  },
  {
    PRDNO: "PRD004",
    DESCP: "Senior Dog Food - Joint Care",
    ItemID: "ITM004",
    SkuName: "PEDIGREE-SENIOR-JOINT-12KG", 
    AssociatedItem: "JOINT-SUPPLEMENTS-001",
    REUPC: "123456789015",
    Brand: "Pedigree",
    SalesDesc: "Specially formulated for senior dogs with joint support"
  },
  {
    PRDNO: "PRD005",
    DESCP: "Kitten Food - Growth Formula",
    ItemID: "ITM005",
    SkuName: "WHISKAS-KITTEN-GROWTH-3KG",
    AssociatedItem: "KITTEN-TOYS-001", 
    REUPC: "123456789016",
    Brand: "Whiskas",
    SalesDesc: "Essential nutrients for growing kittens"
  }
];

// 📝 LEARNING NOTE: Brand options for dropdown
const brandOptions = [
  { value: "", label: "All Brands" },
  { value: "Pedigree", label: "Pedigree" },
  { value: "Whiskas", label: "Whiskas" },
  { value: "Royal Canin", label: "Royal Canin" },
  { value: "Hill's", label: "Hill's" },
  { value: "Purina", label: "Purina" }
];

const ProductSearch = () => {
  const navigate = useNavigate();
  
  // 📝 LEARNING NOTE: Local state for product search (since no backend API)
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [genericSearch, setGenericSearch] = useState("");
  
  // 📝 LEARNING NOTE: Filter state matching your specified fields
  const [filters, setFilters] = useState({
    PRDNO: "",
    DESCP: "",
    REUPC: "",
    Brand: "",
    SalesDesc: "",
    AssocItem: ""
  });

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);

  // 📝 LEARNING NOTE: Reset filters when component mounts
  useEffect(() => {
    setFilters({
      PRDNO: "",
      DESCP: "",
      REUPC: "",
      Brand: "",
      SalesDesc: "",
      AssocItem: ""
    });
    setGenericSearch("");
  }, []);

  // 📝 LEARNING NOTE: Mock API call simulation
  const fetchProducts = useCallback(() => {
    setIsLoading(true);

    // Simulate API delay
    setTimeout(() => {
      let filteredProducts = [...mockProducts];

      // Apply generic search
      if (genericSearch) {
        filteredProducts = filteredProducts.filter(product =>
          Object.values(product).some(value =>
            value?.toString().toLowerCase().includes(genericSearch.toLowerCase())
          )
        );
      }

      // Apply specific filters only when isFilterApplied is true
      if (isFilterApplied) {
        if (filters.PRDNO) {
          filteredProducts = filteredProducts.filter(product =>
            product.PRDNO.toLowerCase().includes(filters.PRDNO.toLowerCase())
          );
        }

        if (filters.DESCP) {
          filteredProducts = filteredProducts.filter(product =>
            product.DESCP.toLowerCase().includes(filters.DESCP.toLowerCase())
          );
        }

        if (filters.REUPC) {
          filteredProducts = filteredProducts.filter(product =>
            product.REUPC?.toLowerCase().includes(filters.REUPC.toLowerCase())
          );
        }

        if (filters.Brand) {
          filteredProducts = filteredProducts.filter(product =>
            product.Brand === filters.Brand
          );
        }

        if (filters.SalesDesc) {
          filteredProducts = filteredProducts.filter(product =>
            product.SalesDesc?.toLowerCase().includes(filters.SalesDesc.toLowerCase())
          );
        }

        if (filters.AssocItem) {
          filteredProducts = filteredProducts.filter(product =>
            product.AssociatedItem.toLowerCase().includes(filters.AssocItem.toLowerCase())
          );
        }
      }

      // Apply pagination
      const startIndex = (pagination.page - 1) * pagination.rowsPerPage;
      const endIndex = startIndex + pagination.rowsPerPage;
      const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

      setProducts(paginatedProducts);
      setTotalCount(filteredProducts.length);
      setIsLoading(false);
    }, 500);
  }, [genericSearch, pagination.page, pagination.rowsPerPage, isFilterApplied, filters]);

  // 📝 LEARNING NOTE: Debounced search handler
  const handleSearch = useCallback(
    debounce((query: string) => {
      setGenericSearch(query);
      setPagination((prev) => ({ ...prev, page: 1 }));
      setIsFilterApplied(true);
    }, 500),
    [],
  );

  // 📝 LEARNING NOTE: Filter change handler
  const handleFilterChange = (name: string, value: string) => {
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // 📝 LEARNING NOTE: Apply filters handler
  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(true);
  };

  // 📝 LEARNING NOTE: Reset filters handler
  const handleResetFilters = () => {
    setFilters({
      PRDNO: "",
      DESCP: "",
      REUPC: "",
      Brand: "",
      SalesDesc: "",
      AssocItem: ""
    });
    setGenericSearch("");
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(false);
    setProducts([]);
    setTotalCount(0);
  };

  // 📝 LEARNING NOTE: Product click handler (for future navigation)
  const handleProductClick = (prdno: string, descp: string) => {
    // TODO: Navigate to product details page
    console.log(`Navigate to product: ${prdno} - ${descp}`);
    // navigate(`/product-details/${prdno}`, { state: { descp } });
  };

  // 📝 LEARNING NOTE: Pagination handler
  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  // 📝 LEARNING NOTE: Fetch products when search or pagination changes
  useEffect(() => {
    if (genericSearch || isFilterApplied) {
      fetchProducts();
    }
  }, [genericSearch, pagination.page, pagination.rowsPerPage, isFilterApplied]);

  // 📝 LEARNING NOTE: Table columns matching your API response structure
  const columns = [
    {
      id: "PRDNO",
      label: "Product No",
      description: "Product number identifier",
      format: (value: string, row: any) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => handleProductClick(value, row.DESCP)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "DESCP",
      label: "Description",
      description: "Product description",
    },
    {
      id: "ItemID",
      label: "Item ID",
      description: "Internal item identifier",
    },
    {
      id: "SkuName",
      label: "SKU Name",
      description: "Stock keeping unit name",
    },
    {
      id: "AssociatedItem",
      label: "Associated Item",
      description: "Related or associated product item",
    },
  ];

  // 📝 LEARNING NOTE: Filter configurations matching your specified fields
  const filterConfigs = [
    {
      name: "PRDNO",
      label: "PRDNO",
      type: "text" as const,
      value: filters.PRDNO,
      onChange: (value: string) => handleFilterChange("PRDNO", value),
    },
    {
      name: "DESCP",
      label: "DESCP",
      type: "text" as const,
      value: filters.DESCP,
      onChange: (value: string) => handleFilterChange("DESCP", value),
    },
    {
      name: "REUPC",
      label: "REUPC",
      type: "text" as const,
      value: filters.REUPC,
      onChange: (value: string) => handleFilterChange("REUPC", value),
    },
    {
      name: "Brand",
      label: "Brand",
      type: "select" as const,
      value: filters.Brand,
      onChange: (value: string) => handleFilterChange("Brand", value),
      options: brandOptions,
    },
    {
      name: "SalesDesc",
      label: "Sales Desc",
      type: "text" as const,
      value: filters.SalesDesc,
      onChange: (value: string) => handleFilterChange("SalesDesc", value),
    },
    {
      name: "AssocItem",
      label: "Assoc Item",
      type: "text" as const,
      value: filters.AssocItem,
      onChange: (value: string) => handleFilterChange("AssocItem", value),
    },
  ];

  return (
    <div>
      {/* 📝 LEARNING NOTE: TopBarLayout with breadcrumb and search - same as Store Search */}
      <TopBarLayout 
        breadcrumbItems={["Product", "Product Search"]} 
        onSearchChange={handleSearch} 
      />

      {/* 📝 LEARNING NOTE: FilterRow with your specified fields */}
      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {/* 📝 LEARNING NOTE: Table component - only show when filters are applied */}
      {isFilterApplied && (
        <TableComponent
          columns={columns}
          rows={products}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          showActions={false}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default ProductSearch;
